import { useCallback, useState, useMemo } from "react";
import { Canvas } from "fabric";
import { updateCanvasFilters } from "@/lib/fabric/utils";

export interface FilterState {
  brightness: number;
  contrast: number;
  grayscale: boolean;
  invert: boolean;
  sharpness: number;
  gammaR: number;
  gammaG: number;
  gammaB: number;
}

export interface FilterHandlers {
  setBrightness: (value: number) => void;
  setContrast: (value: number) => void;
  setGrayscale: (value: boolean) => void;
  setInvert: (value: boolean) => void;
  setSharpness: (value: number) => void;
  setGammaR: (value: number) => void;
  setGammaG: (value: number) => void;
  setGammaB: (value: number) => void;
}

export interface FilterManagementState {
  filters: FilterState;
  filterHandlers: FilterHandlers;
  updateFilter: (keyOrConfig: string | object, value?: number | boolean) => void;
}

export const useFilterManagement = (
  initialFilters: FilterState,
  fabricCanvas: React.RefObject<Canvas | null>
): FilterManagementState => {
  const [filters, setFilters] = useState<FilterState>(initialFilters);

  const updateFilter = useCallback((keyOrConfig: string | object, value?: number | boolean) => {
    if (typeof keyOrConfig === "object") {
      setFilters((prev) => ({ ...prev, ...keyOrConfig }));
    } else {
      setFilters((prev) => ({ ...prev, [keyOrConfig]: value }));
    }
  }, []);

  const filterHandlers = useMemo(() => {
    const createHandler = (key: string) => (value: number | boolean) => {
      updateFilter(key, value);
      if (fabricCanvas.current) updateCanvasFilters(fabricCanvas.current, { [key]: value });
    };

    return {
      setBrightness: createHandler("brightness"),
      setContrast: createHandler("contrast"),
      setGrayscale: createHandler("grayscale"),
      setInvert: createHandler("invert"),
      setSharpness: createHandler("sharpness"),
      setGammaR: createHandler("gammaR"),
      setGammaG: createHandler("gammaG"),
      setGammaB: createHandler("gammaB"),
    };
  }, [updateFilter, fabricCanvas]);

  return {
    filters,
    filterHandlers,
    updateFilter,
  };
};
