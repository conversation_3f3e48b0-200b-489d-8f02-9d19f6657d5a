import { Canvas } from "fabric";
import { useCallback, useEffect, useRef } from "react";
import { UseResponsiveCanvasProps, CropData } from "@/shared/types";
import { DEFAULT_IMAGE_WIDTH, DEFAULT_IMAGE_HEIGHT } from "@/lib/fabric/constants";
import {
  calculateFittedCanvasDimensions,
  shouldResize,
  applyMinimumDimensions,
  scaleCanvasObjects,
  positionBackgroundImage,
  calculateCropViewportTransform,
  updateCropDataDimensions,
} from "@/lib/fabric/utils/resizeUtils";

const handleCroppedCanvasResize = (
  canvas: Canvas,
  container: HTMLElement,
  cropData: CropData,
  setCropData?: (data: CropData) => void
) => {
  if (!cropData?.normalizedCropRect || !cropData.canvasDimensions) return;

  const containerRect = container.getBoundingClientRect();
  const currentCanvasWidth = canvas.getWidth();
  const currentCanvasHeight = canvas.getHeight();

  const cropAspectRatio = cropData.normalizedCropRect.width / cropData.normalizedCropRect.height;
  const { width: targetCanvasWidth, height: targetCanvasHeight } = calculateFittedCanvasDimensions(
    cropAspectRatio,
    1,
    containerRect.width,
    containerRect.height
  );

  // Only resize if there's a significant change in target dimensions
  if (
    !shouldResize(currentCanvasWidth, currentCanvasHeight, targetCanvasWidth, targetCanvasHeight)
  ) {
    return;
  }

  const currentVpt = canvas.viewportTransform;
  if (!currentVpt) return;

  const bgImage = canvas.backgroundImage;
  if (!bgImage) return;

  const imageWidth = bgImage.width || DEFAULT_IMAGE_WIDTH;
  const imageHeight = bgImage.height || DEFAULT_IMAGE_HEIGHT;
  const imageScale = bgImage.scaleX || 1;
  const scaledImageWidth = imageWidth * imageScale;
  const scaledImageHeight = imageHeight * imageScale;

  const cropLeft = cropData.normalizedCropRect.left * scaledImageWidth;
  const cropTop = cropData.normalizedCropRect.top * scaledImageHeight;
  const cropWidth = cropData.normalizedCropRect.width * scaledImageWidth;
  const cropHeight = cropData.normalizedCropRect.height * scaledImageHeight;

  canvas.setDimensions({ width: targetCanvasWidth, height: targetCanvasHeight });

  const newVpt = calculateCropViewportTransform(
    targetCanvasWidth,
    targetCanvasHeight,
    cropLeft,
    cropTop,
    cropWidth,
    cropHeight
  );

  canvas.setViewportTransform(newVpt);
  canvas.renderAll();

  if (setCropData) {
    const updatedCropData = updateCropDataDimensions(
      cropData,
      containerRect.width,
      containerRect.height
    );
    setCropData(updatedCropData);
  }
};

const handleOriginalCanvasResize = (canvas: Canvas, container: HTMLElement) => {
  const containerRect = container.getBoundingClientRect();
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  const { width: fittedWidth, height: fittedHeight } = calculateFittedCanvasDimensions(
    currentWidth,
    currentHeight,
    containerRect.width,
    containerRect.height
  );

  const { width: targetWidth, height: targetHeight } = applyMinimumDimensions(
    fittedWidth,
    fittedHeight
  );

  // Only resize if target dimensions differ significantly from current
  if (!shouldResize(currentWidth, currentHeight, targetWidth, targetHeight)) {
    return;
  }

  const scaleX = targetWidth / currentWidth;
  const scaleY = targetHeight / currentHeight;
  const imageScale = Math.min(scaleX, scaleY);
  const actualWidth = currentWidth * imageScale;
  const actualHeight = currentHeight * imageScale;

  canvas.setDimensions({ width: actualWidth, height: actualHeight });
  canvas.renderOnAddRemove = false;

  scaleCanvasObjects(canvas, imageScale);

  const bgImg = canvas.backgroundImage;
  if (bgImg) {
    positionBackgroundImage(bgImg, actualWidth, actualHeight, imageScale);
  }

  canvas.renderOnAddRemove = true;
  canvas.requestRenderAll();
};

export const useResponsiveCanvas = ({
  fabricCanvas,
  containerRef,
  cropData,
  setCropData,
}: UseResponsiveCanvasProps) => {
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const resizeCanvas = useCallback(() => {
    if (!fabricCanvas?.current || !containerRef.current) return;

    const canvas = fabricCanvas.current;

    if (cropData?.isCropped && cropData.normalizedCropRect && cropData.canvasDimensions) {
      handleCroppedCanvasResize(canvas, containerRef.current, cropData, setCropData);
    } else {
      handleOriginalCanvasResize(canvas, containerRef.current);
    }
  }, [fabricCanvas, containerRef, cropData, setCropData]);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    resizeObserverRef.current = new ResizeObserver(() => {
      resizeCanvas();
    });

    resizeObserverRef.current.observe(container);

    return () => {
      resizeObserverRef.current?.disconnect();
      resizeObserverRef.current = null;
    };
  }, [containerRef, resizeCanvas]);

  return {
    resizeCanvas,
  };
};
