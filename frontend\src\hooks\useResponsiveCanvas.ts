import { Canvas, Textbox } from "fabric";
import { useCallback, useEffect, useRef } from "react";
import { UseResponsiveCanvasProps, CropData } from "@/shared/types";

const handleCroppedCanvasResize = (
  canvas: Canvas,
  container: HTMLElement,
  cropData: CropData,
  setCropData?: (data: CropData) => void
) => {
  if (!cropData?.normalizedCropRect || !cropData.canvasDimensions) return;

  const containerRect = container.getBoundingClientRect();
  const currentCanvasWidth = canvas.getWidth();
  const currentCanvasHeight = canvas.getHeight();

  const cropAspectRatio = cropData.normalizedCropRect.width / cropData.normalizedCropRect.height;
  let targetCanvasWidth = containerRect.width;
  let targetCanvasHeight = targetCanvasWidth / cropAspectRatio;

  if (targetCanvasHeight > containerRect.height) {
    targetCanvasHeight = containerRect.height;
    targetCanvasWidth = targetCanvasHeight * cropAspectRatio;
  }

  // Only resize if there's a significant change in target dimensions
  if (
    Math.abs(targetCanvasWidth - currentCanvasWidth) < 5 &&
    Math.abs(targetCanvasHeight - currentCanvasHeight) < 5
  ) {
    return;
  }

  const newCanvasWidth = targetCanvasWidth;
  const newCanvasHeight = targetCanvasHeight;

  const currentVpt = canvas.viewportTransform;
  if (!currentVpt) return;

  const bgImage = canvas.backgroundImage;
  if (!bgImage) return;

  const imageWidth = bgImage.width || 512;
  const imageHeight = bgImage.height || 512;
  const imageScale = bgImage.scaleX || 1;
  const scaledImageWidth = imageWidth * imageScale;
  const scaledImageHeight = imageHeight * imageScale;

  const cropLeft = cropData.normalizedCropRect.left * scaledImageWidth;
  const cropTop = cropData.normalizedCropRect.top * scaledImageHeight;
  const cropWidth = cropData.normalizedCropRect.width * scaledImageWidth;
  const cropHeight = cropData.normalizedCropRect.height * scaledImageHeight;

  const cropScale = Math.max(newCanvasWidth / cropWidth, newCanvasHeight / cropHeight);

  const newTranslateX = -cropLeft * cropScale;
  const newTranslateY = -cropTop * cropScale;

  canvas.setDimensions({ width: newCanvasWidth, height: newCanvasHeight });

  const newVpt: [number, number, number, number, number, number] = [
    cropScale,
    0,
    0,
    cropScale,
    newTranslateX,
    newTranslateY,
  ];

  canvas.setViewportTransform(newVpt);
  canvas.renderAll();

  if (setCropData) {
    const updatedCropData: CropData = {
      ...cropData,
      canvasDimensions: {
        width: containerRect.width,
        height: containerRect.height,
      },
    };
    setCropData(updatedCropData);
  }
};

const handleOriginalCanvasResize = (canvas: Canvas, container: HTMLElement) => {
  const containerRect = container.getBoundingClientRect();
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  const currentAspectRatio = currentWidth / currentHeight;
  let targetWidth = containerRect.width;
  let targetHeight = targetWidth / currentAspectRatio;

  if (targetHeight > containerRect.height) {
    targetHeight = containerRect.height;
    targetWidth = targetHeight * currentAspectRatio;
  }

  targetWidth = Math.max(targetWidth, 300);
  targetHeight = Math.max(targetHeight, 200);

  // Only resize if target dimensions differ significantly from current
  if (Math.abs(currentWidth - targetWidth) < 5 && Math.abs(currentHeight - targetHeight) < 5) {
    return;
  }

  const scaleX = targetWidth / currentWidth;
  const scaleY = targetHeight / currentHeight;
  const imageScale = Math.min(scaleX, scaleY);
  const actualWidth = currentWidth * imageScale;
  const actualHeight = currentHeight * imageScale;

  canvas.setDimensions({ width: actualWidth, height: actualHeight });
  canvas.renderOnAddRemove = false;

  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "background-image") {
      obj.set({
        scaleX: (obj.scaleX || 1) * imageScale,
        scaleY: (obj.scaleY || 1) * imageScale,
        left: (obj.left || 0) * imageScale,
        top: (obj.top || 0) * imageScale,
      });

      if ("strokeUniform" in obj) obj.strokeUniform = true;

      if (obj.type === "textbox" || obj.type === "text") {
        const textbox = obj as Textbox;
        textbox.fontSize = 16;
        textbox.set({
          scaleX: 1,
          scaleY: 1,
          lockScalingX: true,
          lockScalingY: true,
          hasControls: false,
        });
      }

      obj.setCoords();
    }
  });

  const bgImg = canvas.backgroundImage;
  if (bgImg) {
    const currentAngle = bgImg.angle || 0;
    const currentFlipX = bgImg.flipX || false;
    const currentFlipY = bgImg.flipY || false;

    bgImg.scaleX = (bgImg.scaleX || 1) * imageScale;
    bgImg.scaleY = (bgImg.scaleY || 1) * imageScale;

    if ([0, 180].includes(currentAngle)) {
      bgImg.left = 0;
      bgImg.top = 0;
    } else {
      bgImg.set({
        left: actualWidth / 2,
        top: actualHeight / 2,
        originX: "center",
        originY: "center",
      });
    }

    bgImg.angle = currentAngle;
    bgImg.flipX = currentFlipX;
    bgImg.flipY = currentFlipY;
  }

  canvas.renderOnAddRemove = true;
  canvas.requestRenderAll();
};

export const useResponsiveCanvas = ({
  fabricCanvas,
  containerRef,
  cropData,
  setCropData,
}: UseResponsiveCanvasProps) => {
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const resizeCanvas = useCallback(() => {
    if (!fabricCanvas?.current || !containerRef.current) return;

    const canvas = fabricCanvas.current;

    if (cropData?.isCropped && cropData.normalizedCropRect && cropData.canvasDimensions) {
      handleCroppedCanvasResize(canvas, containerRef.current, cropData, setCropData);
    } else {
      handleOriginalCanvasResize(canvas, containerRef.current);
    }
  }, [fabricCanvas, containerRef, cropData, setCropData]);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    resizeObserverRef.current = new ResizeObserver(() => {
      resizeCanvas();
    });

    resizeObserverRef.current.observe(container);

    return () => {
      resizeObserverRef.current?.disconnect();
      resizeObserverRef.current = null;
    };
  }, [containerRef, resizeCanvas]);

  return {
    resizeCanvas,
  };
};
