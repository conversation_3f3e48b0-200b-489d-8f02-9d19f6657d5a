import { Canvas } from "fabric";
import { LoadImageOptions } from "@/shared/types";

export const loadCanvasImage = async (
  canvas: Canvas,
  url: string,
  options?: LoadImageOptions
): Promise<{ scale: number; width: number; height: number }> => {
  const { FabricImage } = await import("fabric");
  const img = await FabricImage.fromURL(url, { crossOrigin: "anonymous" });
  img.set({ left: 0, top: 0, selectable: false, evented: false, name: "background-image" });

  let width: number, height: number, scale: number;

  if (options?.containerRect) {
    // Fit image within container, preserving aspect ratio
    const aspect = (img.width || 512) / (img.height || 512);
    let newWidth = options.containerRect.width;
    let newHeight = newWidth / aspect;
    if (newHeight > options.containerRect.height) {
      newHeight = options.containerRect.height;
      newWidth = newHeight * aspect;
    }
    scale = newWidth / (img.width || 512);
    width = newWidth;
    height = newHeight;
  } else {
    // Default: fit to window height
    scale = window.innerHeight / (img.height || 512);
    width = Math.round((img.width || 512) * scale);
    height = Math.round((img.height || 512) * scale);
  }

  canvas.setDimensions({ width, height });
  img.set({ scaleX: scale, scaleY: scale });
  canvas.backgroundImage = img;
  canvas.renderAll();

  return { scale, width, height };
};
