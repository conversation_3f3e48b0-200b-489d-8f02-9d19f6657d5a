import { useState } from "react";
import { Canvas } from "fabric";
import { TransformState } from "@/shared/types";
import {
  createHandleRotate,
  createHandleFlipHorizontal,
  createHandleFlipVertical,
} from "@/lib/fabric/handlers";
import { rotateCanvas, flipCanvasHorizontal, flipCanvasVertical } from "@/lib/fabric/utils";

export interface ImageTransformsState {
  transformState: TransformState;
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>;
  handleRotate: () => void;
  handleFlipHorizontal: () => void;
  handleFlipVertical: () => void;
  applySavedTransforms: () => void;
}

export const useImageTransforms = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialTransformState: TransformState = {
    rotations: 0,
    flipHorizontal: false,
    flipVertical: false,
  }
): ImageTransformsState => {
  const [transformState, setTransformState] = useState<TransformState>(initialTransformState);

  const handleRotate = createHandleRotate(fabricCanvas, setTransformState);
  const handleFlipHorizontal = createHandleFlipHorizontal(fabricCanvas, setTransformState);
  const handleFlipVertical = createHandleFlipVertical(fabricCanvas, setTransformState);

  const applySavedTransforms = () => {
    if (!fabricCanvas.current) return;

    const canvas = fabricCanvas.current;

    for (let i = 0; i < transformState.rotations; i++) {
      rotateCanvas(canvas);
    }

    if (transformState.flipHorizontal) {
      flipCanvasHorizontal(canvas);
    }

    if (transformState.flipVertical) {
      flipCanvasVertical(canvas);
    }
  };

  return {
    transformState,
    setTransformState,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    applySavedTransforms,
  };
};
