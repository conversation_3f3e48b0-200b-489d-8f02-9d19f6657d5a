/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from "react";
import { Textbox, Rect, Line, Circle, PencilBrush, Canvas } from "fabric";
import { cleanupOrphanedMeasurementTexts, updateMeasurementText } from "@/lib/fabric/utils";
import {
  FaSave,
  FaPen,
  FaSun,
  FaAdjust,
  FaFont,
  FaPalette,
  FaExchangeAlt,
  FaSquare,
  FaMinus,
  FaCircle,
  FaRedo,
  FaArrowsAltH,
  FaArrowsAltV,
  FaMousePointer,
  FaSearchPlus,
  FaGem,
  FaUndo,
  FaCrop,
  FaRuler,
  FaImage,
} from "react-icons/fa";
import { ImageToolbarProps, FabricMeasurementLine, ToolMode } from "@/shared/types";

const FabricToolbar: React.FC<ImageToolbarProps> = ({
  fabricCanvas,
  brightness,
  contrast,
  grayscale,
  invert,
  sharpness,
  gammaR,
  gammaG,
  gammaB,
  onBrightnessChange,
  onContrastChange,
  onGrayscaleChange,
  onInvertChange,
  onSharpnessChange,
  onGammaRChange,
  onGammaGChange,
  onGammaBChange,
  onRotate,
  onFlipHorizontal,
  onFlipVertical,
  onUndo,
  canUndo = false,
  onSave,
  onShowOriginal,
  onShapeCreated,
  onCrop,
  disableGrayscale = false,
  disableGamma = false,
  disableUndoTracking,
  enableUndoTracking,
  isShowingOriginal = false,
  hasPerformedCrop = false,
}) => {
  const [activeMode, setActiveMode] = useState<ToolMode | null>(null);
  const [startPoint, setStartPoint] = useState<{ x: number; y: number } | null>(null);
  const [currentShape, setCurrentShape] = useState<
    Rect | Circle | Line | FabricMeasurementLine | Textbox | null
  >(null);

  const setToolMode = (mode: string) => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;
    canvas.selection = false;
    canvas.discardActiveObject();
    canvas.forEachObject((obj) => {
      const objName = (obj as unknown as Record<string, unknown>)?.name;
      if (objName !== "background-image") {
        obj.selectable = false;
        obj.evented = false;
      }
    });
    canvas.defaultCursor = "crosshair";
    canvas.isDrawingMode = false;
    canvas.selection = false;

    if (mode === "freehand") {
      canvas.isDrawingMode = true;
    } else if (mode !== "select") {
      canvas.isDrawingMode = false;
    } else if (mode === "select") {
      canvas.defaultCursor = "default";
      canvas.isDrawingMode = false;
      canvas.selection = true;

      canvas.forEachObject((obj) => {
        const objName = (obj as unknown as Record<string, unknown>)?.name;
        if (objName !== "background-image") {
          obj.selectable = true;
          obj.evented = true;
          obj.hasControls = true;
          obj.hasBorders = true;
          obj.setCoords();
        }
      });
    }
    canvas.renderAll();
  };

  const tools = [
    {
      icon: FaMousePointer,
      title: "Select/Move objects",
      mode: "select" as ToolMode,
    },
    { icon: FaPen, title: "Draw freehand paths", mode: "freehand" as ToolMode },
    { icon: FaFont, title: "Add text", mode: "text" as ToolMode },
    { icon: FaSquare, title: "Add rectangle", mode: "rect" as ToolMode },
    { icon: FaMinus, title: "Add line", mode: "line" as ToolMode },
    { icon: FaCircle, title: "Add circle", mode: "circle" as ToolMode },
    { icon: FaCrop, title: "Crop image", mode: "crop" as ToolMode },
    { icon: FaRuler, title: "Measure distance", mode: "measure" as ToolMode },
  ];

  const sliders = [
    {
      icon: FaSun,
      label: "Bright",
      value: brightness,
      onChange: onBrightnessChange,
      min: 0.1,
      max: 2,
      step: 0.1,
    },
    {
      icon: FaAdjust,
      label: "Contrast",
      value: contrast,
      onChange: onContrastChange,
      min: 0.1,
      max: 2,
      step: 0.1,
    },
    {
      icon: FaSearchPlus,
      label: "Sharpness",
      value: sharpness,
      onChange: onSharpnessChange,
      min: 0.1,
      max: 2,
      step: 0.1,
    },
  ];

  const gammaSliders = [
    {
      label: "R",
      value: gammaR,
      onChange: onGammaRChange,
      className: "gamma-red",
    },
    {
      label: "G",
      value: gammaG,
      onChange: onGammaGChange,
      className: "gamma-green",
    },
    {
      label: "B",
      value: gammaB,
      onChange: onGammaBChange,
      className: "gamma-blue",
    },
  ];

  const getToolConfig = (mode: ToolMode, canvas?: Canvas) => {
    // Get viewport transform scale to adjust stroke width for cropped images
    const vpt = canvas?.viewportTransform;
    const viewportScale = vpt ? vpt[0] : 1; // vpt[0] is the x-scale factor
    const adjustedStrokeWidth = 2 / viewportScale; // Compensate for viewport scaling

    switch (mode) {
      case "freehand":
        return { color: "red", width: 3 / viewportScale, strokeUniform: true };
      case "text":
        return {
          fontSize: 20,
          fill: "red",
          width: 200,
          selectable: false,
          evented: false,
          strokeUniform: true,
        };
      case "rect":
        return {
          width: 1,
          height: 1,
          fill: "transparent",
          stroke: "red",
          strokeWidth: adjustedStrokeWidth,
          strokeUniform: true,
          selectable: false,
          evented: false,
        };
      case "circle":
        return {
          radius: 1,
          fill: "transparent",
          stroke: "red",
          strokeWidth: adjustedStrokeWidth,
          selectable: false,
          evented: false,
          strokeUniform: true,
        };
      case "line":
        return {
          stroke: "red",
          strokeWidth: adjustedStrokeWidth,
          selectable: false,
          evented: false,
          strokeUniform: true,
        };
      case "crop":
        return {
          width: 1,
          height: 1,
          fill: "rgba(255, 255, 255, .1)",
          stroke: "#ff0000",
          strokeWidth: 0.5,
          strokeDashArray: [5, 5],
          selectable: false,
          evented: false,
          strokeUniform: true,
          name: "crop-rect",
        };
      case "measure":
        return {
          strokeWidth: adjustedStrokeWidth,
          fill: "#ff6b35",
          stroke: "#ff6b35",
          originX: "center",
          originY: "center",
          selectable: false,
          evented: false,
          hasControls: false,
          hasBorders: false,
          lockScalingFlip: true,
          name: "measurement-line",
          strokeUniform: true,
        };
      default:
        return {};
    }
  };

  const constrainToCanvas = (pointer: { x: number; y: number }, canvas: Canvas) => {
    // For crop tool, allow free drawing within visible canvas area
    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();

    return {
      x: Math.max(0, Math.min(pointer.x, canvasWidth - 1)),
      y: Math.max(0, Math.min(pointer.y, canvasHeight - 1)),
    };
  };

  const createInitialShape = (
    mode: ToolMode,
    pointer: { x: number; y: number },
    canvas: Canvas
  ) => {
    const config = getToolConfig(mode, canvas) as any;

    // Transform coordinates for all tools when viewport transform is applied (after crop)
    const vpt = canvas.viewportTransform;
    const needsTransform = vpt && (vpt[0] !== 1 || vpt[4] !== 0 || vpt[5] !== 0);

    let transformedPointer = pointer;
    if (needsTransform) {
      transformedPointer = {
        x: (pointer.x - vpt[4]) / vpt[0],
        y: (pointer.y - vpt[5]) / vpt[3],
      };
    }

    switch (mode) {
      case "text":
        return new Textbox("Text", {
          left: transformedPointer.x,
          top: transformedPointer.y,
          ...config,
        });
      case "rect":
        return new Rect({
          left: transformedPointer.x,
          top: transformedPointer.y,
          width: 1,
          height: 1,
          ...config,
        });
      case "line":
        return new Line(
          [transformedPointer.x, transformedPointer.y, transformedPointer.x, transformedPointer.y],
          {
            ...config,
          }
        );
      case "circle":
        return new Circle({
          left: transformedPointer.x,
          top: transformedPointer.y,
          radius: 1,
          ...config,
        });
      case "crop":
        return new Rect({
          left: transformedPointer.x,
          top: transformedPointer.y,
          ...config,
        });
      case "measure": {
        const line = new Line(
          [transformedPointer.x, transformedPointer.y, transformedPointer.x, transformedPointer.y],
          {
            ...config,
          }
        ) as Line & { id: string };
        line.id = `line_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        return line;
      }
    }
  };

  const updateShapeSize = (
    shape: Rect | Circle | Line | FabricMeasurementLine | Textbox,
    startPoint: { x: number; y: number },
    currentPoint: { x: number; y: number },
    mode: ToolMode,
    canvas: Canvas
  ) => {
    const canvasWidth = canvas.getWidth();
    const canvasHeight = canvas.getHeight();

    const width = Math.abs(currentPoint.x - startPoint.x);
    const height = Math.abs(currentPoint.y - startPoint.y);
    const left = Math.min(startPoint.x, currentPoint.x);
    const top = Math.min(startPoint.y, currentPoint.y);

    switch (mode) {
      case "text":
        break;
      case "rect":
      case "crop":
        shape.set({
          left: left,
          top: top,
          width: Math.max(1, width),
          height: Math.max(1, height),
        });
        break;
      case "line":
      case "measure":
        shape.set({
          x1: startPoint.x,
          y1: startPoint.y,
          x2: currentPoint.x,
          y2: currentPoint.y,
        });
        break;
      case "circle": {
        const maxRadius = Math.min(
          (canvasWidth - left) / 2,
          (canvasHeight - top) / 2,
          width / 2,
          height / 2
        );
        const radius = Math.max(1, maxRadius);
        shape.set({
          left: left,
          top: top,
          radius: radius,
        });
        break;
      }
    }
  };

  useEffect(() => {
    if (!fabricCanvas?.current) return;

    const canvas = fabricCanvas.current;
    const config = getToolConfig("freehand", canvas) as any;
    canvas.freeDrawingBrush = new PencilBrush(canvas);
    canvas.freeDrawingBrush.color = config.color;
    canvas.freeDrawingBrush.width = config.width;
    // canvas.freeDrawingBrush.limitedToCanvasSize = true;
    if (activeMode) {
      setToolMode(activeMode);
    }

    const mouseDownHandler = (e: { pointer: { x: number; y: number } }) => {
      if (!activeMode || activeMode === "select" || !e.pointer) {
        return;
      }

      // Check if the current active mode is disabled
      if (isShowingOriginal) {
        return;
      }

      const constrainedPointer = constrainToCanvas(e.pointer, canvas);

      // Transform coordinates for viewport transform (after crop)
      const vpt = canvas.viewportTransform;
      const needsTransform = vpt && (vpt[0] !== 1 || vpt[4] !== 0 || vpt[5] !== 0);

      let transformedPointer = constrainedPointer;
      if (needsTransform) {
        transformedPointer = {
          x: (constrainedPointer.x - vpt[4]) / vpt[0],
          y: (constrainedPointer.y - vpt[5]) / vpt[3],
        };
      }

      if (activeMode === "text") {
        const objects = canvas.getObjects();
        const clickedObject = objects.find((obj) => {
          if (obj.type === "textbox") {
            const objBounds = obj.getBoundingRect();
            return (
              constrainedPointer.x >= objBounds.left &&
              constrainedPointer.x <= objBounds.left + objBounds.width &&
              constrainedPointer.y >= objBounds.top &&
              constrainedPointer.y <= objBounds.top + objBounds.height
            );
          }
          return false;
        });

        if (clickedObject) {
          canvas.setActiveObject(clickedObject);
          (clickedObject as Textbox).enterEditing();
          return;
        }

        const shape = createInitialShape(activeMode, transformedPointer, canvas);
        if (!shape) return;

        canvas.add(shape);
        canvas.setActiveObject(shape);
        const textbox = shape as Textbox;
        textbox.enterEditing();
        textbox.selectAll();
        if (onShapeCreated) onShapeCreated();
        return;
      }

      const shape = createInitialShape(activeMode, transformedPointer, canvas);
      if (!shape) return;

      canvas.add(shape);
      setStartPoint(transformedPointer);
      setCurrentShape(shape);
    };

    const mouseMoveHandler = (e: { pointer: { x: number; y: number } }) => {
      if (!currentShape || !startPoint || !e.pointer || !activeMode) return;

      // Check if the current active mode is disabled
      if (isShowingOriginal) {
        return;
      }

      const constrainedPointer = constrainToCanvas(e.pointer, canvas);

      // Transform coordinates for viewport transform (after crop)
      const vpt = canvas.viewportTransform;
      const needsTransform = vpt && (vpt[0] !== 1 || vpt[4] !== 0 || vpt[5] !== 0);

      let transformedPointer = constrainedPointer;
      if (needsTransform) {
        transformedPointer = {
          x: (constrainedPointer.x - vpt[4]) / vpt[0],
          y: (constrainedPointer.y - vpt[5]) / vpt[3],
        };
      }

      updateShapeSize(currentShape, startPoint, transformedPointer, activeMode, canvas);
      if (activeMode === "measure") {
        const line = currentShape as FabricMeasurementLine;
        if (line.measurementText) {
          disableUndoTracking?.();
          canvas.remove(line.measurementText);
          enableUndoTracking?.();
          line.measurementText = undefined;
        }
        disableUndoTracking?.();
        updateMeasurementText(canvas, line);
        enableUndoTracking?.();
      }
      canvas.renderAll();
    };
    const mouseUpHandler = () => {
      if (!currentShape) {
        setStartPoint(null);
        setCurrentShape(null);
        return;
      }

      // Check if the current active mode is disabled
      if (isShowingOriginal) {
        setStartPoint(null);
        setCurrentShape(null);
        return;
      }

      if (activeMode === "crop" && onCrop) onCrop();

      if (activeMode === "measure") {
        const line = currentShape as FabricMeasurementLine;
        if (!line.measurementText) {
          disableUndoTracking?.();
          updateMeasurementText(canvas, line);
          enableUndoTracking?.();
        }
      }

      if (onShapeCreated && activeMode !== "crop") onShapeCreated();

      setStartPoint(null);
      setCurrentShape(null);
    };

    const disposeMouseDown = canvas.on("mouse:down", mouseDownHandler);
    const disposeMouseMove = canvas.on("mouse:move", mouseMoveHandler);
    const disposeMouseUp = canvas.on("mouse:up", mouseUpHandler);

    return () => {
      disposeMouseDown();
      disposeMouseMove();
      disposeMouseUp();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    fabricCanvas,
    activeMode,
    startPoint,
    currentShape,
    onShapeCreated,
    hasPerformedCrop,
    isShowingOriginal,
  ]);

  const handleSave = () => {
    if (onSave) onSave();
  };

  return (
    <div className={`fabric-toolbar-vertical ${isShowingOriginal ? "disabled" : ""}`}>
      <div className="annotation-tools">
        <div className="tool-grid">
          {tools.map((tool) => {
            const IconComponent = tool.icon;
            const isDisabled = isShowingOriginal;
            const isCropInactive = tool.mode === "crop" && hasPerformedCrop;

            return (
              <button
                key={tool.mode}
                className={`tool-btn ${activeMode === tool.mode ? "active" : ""} ${
                  isDisabled ? "disabled" : ""
                } ${isCropInactive ? "inactive" : ""}`}
                onClick={async () => {
                  if (isDisabled) return;

                  // Special handling for crop button when image is already cropped
                  if (tool.mode === "crop" && hasPerformedCrop) {
                    if (onCrop) {
                      await onCrop();
                      // After restoring original image, activate crop mode
                      setActiveMode("crop");
                    }
                    return;
                  }

                  if (
                    activeMode === "measure" &&
                    tool.mode !== "measure" &&
                    fabricCanvas?.current
                  ) {
                    cleanupOrphanedMeasurementTexts(fabricCanvas.current);
                  }
                  setActiveMode(tool.mode);
                }}
                disabled={isDisabled}
                title={
                  isShowingOriginal
                    ? "Disabled in show original mode"
                    : tool.mode === "crop" && hasPerformedCrop
                    ? "Restore original image"
                    : tool.title
                }
              >
                <IconComponent />
              </button>
            );
          })}

          <button
            className={`tool-btn ${isShowingOriginal ? "disabled" : ""}`}
            onClick={isShowingOriginal ? undefined : onRotate}
            disabled={isShowingOriginal}
            title="Rotate selected object"
          >
            <FaRedo />
          </button>
          <button
            className={`tool-btn ${isShowingOriginal ? "disabled" : ""}`}
            onClick={isShowingOriginal ? undefined : onFlipHorizontal}
            disabled={isShowingOriginal}
            title="Flip horizontal"
          >
            <FaArrowsAltH />
          </button>
          <button
            className={`tool-btn ${isShowingOriginal ? "disabled" : ""}`}
            onClick={isShowingOriginal ? undefined : onFlipVertical}
            disabled={isShowingOriginal}
            title="Flip vertical"
          >
            <FaArrowsAltV />
          </button>
          <button
            className={`tool-btn ${grayscale ? "active" : ""} ${
              disableGrayscale || isShowingOriginal ? "disabled" : ""
            }`}
            onClick={() => !disableGrayscale && !isShowingOriginal && onGrayscaleChange(!grayscale)}
            disabled={disableGrayscale || isShowingOriginal}
            title="Toggle grayscale"
          >
            <FaPalette />
          </button>
          <button
            className={`tool-btn ${invert ? "active" : ""} ${isShowingOriginal ? "disabled" : ""}`}
            onClick={() => !isShowingOriginal && onInvertChange(!invert)}
            disabled={isShowingOriginal}
            title="Toggle invert"
          >
            <FaExchangeAlt />
          </button>
          {onShowOriginal && (
            <button
              className={`tool-btn ${isShowingOriginal ? "active" : ""}`}
              onClick={onShowOriginal}
              title={isShowingOriginal ? "Exit show original mode" : "Show original image"}
            >
              <FaImage />
            </button>
          )}
        </div>
      </div>

      {sliders.map((slider) => {
        const IconComponent = slider.icon;
        return (
          <div key={slider.label} className="control-item">
            <div className="control-icon" title={slider.label}>
              <IconComponent />
            </div>
            <input
              type="range"
              min={slider.min}
              max={slider.max}
              step={slider.step}
              value={slider.value}
              onChange={(e) => !isShowingOriginal && slider.onChange(parseFloat(e.target.value))}
              className="horizontal-slider"
              title={`${slider.label}: ${(slider.value || 0).toFixed(1)}`}
              disabled={isShowingOriginal}
            />
            <span>{(slider.value || 0).toFixed(1)}</span>
          </div>
        );
      })}

      <div className={`gamma-controls ${disableGamma || isShowingOriginal ? "disabled" : ""}`}>
        <div className="gamma-icon" title="Gamma RGB">
          <FaGem />
        </div>
        <div className="gamma-sliders">
          {gammaSliders.map((gamma) => (
            <div key={gamma.label} className="gamma-slider-item">
              <div className={`gamma-label ${gamma.label.toLowerCase()}`}>{gamma.label}</div>
              <input
                type="range"
                min="1"
                max="2.2"
                step="0.05"
                value={gamma.value}
                onChange={(e) =>
                  !disableGamma && !isShowingOriginal && gamma.onChange(parseFloat(e.target.value))
                }
                className={`horizontal-slider ${gamma.className}`}
                disabled={disableGamma || isShowingOriginal}
                title={`Gamma ${gamma.label}: ${(gamma.value || 1).toFixed(2)}`}
              />
              <span className="gamma-value">{(gamma.value || 1).toFixed(2)}</span>
            </div>
          ))}
        </div>
      </div>

      {onUndo && (
        <button
          className={`undo-btn ${!canUndo || isShowingOriginal ? "disabled" : ""}`}
          onClick={canUndo && !isShowingOriginal ? onUndo : undefined}
          disabled={!canUndo || isShowingOriginal}
          title="Undo"
        >
          <FaUndo />
        </button>
      )}

      <button
        className={`save-btn ${isShowingOriginal ? "disabled" : ""}`}
        onClick={isShowingOriginal ? undefined : handleSave}
        disabled={isShowingOriginal}
        title="Save"
      >
        <FaSave />
      </button>
    </div>
  );
};

export default FabricToolbar;
