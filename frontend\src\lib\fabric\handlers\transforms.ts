import { Canvas } from "fabric";
import { rotateCanvas, flipCanvasHorizontal, flipCanvasVertical } from "@/lib/fabric/utils";
import { TransformState } from "@/shared/types";

export const createHandleRotate = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      rotateCanvas(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, rotations: (prev.rotations + 1) % 4 }));
    }
  };
};

export const createHandleFlipHorizontal = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      flipCanvasHorizontal(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipHorizontal: !prev.flipHorizontal }));
    }
  };
};

export const createHandleFlipVertical = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      flipCanvasVertical(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipVertical: !prev.flipVertical }));
    }
  };
};

export const createHandleReset = (
  transformState: TransformState,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return (canvas: Canvas) => {
    Array.from({ length: (4 - transformState.rotations) % 4 }, () => rotateCanvas(canvas));
    if (transformState.flipHorizontal) flipCanvasHorizontal(canvas);
    if (transformState.flipVertical) flipCanvasVertical(canvas);
    setTransformState({ rotations: 0, flipHorizontal: false, flipVertical: false });
  };
};
