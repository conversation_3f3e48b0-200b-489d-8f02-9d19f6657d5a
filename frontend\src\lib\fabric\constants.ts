// Default image dimensions when actual dimensions are not available
export const DEFAULT_IMAGE_WIDTH = 512;
export const DEFAULT_IMAGE_HEIGHT = 512;

// Canvas sizing constants
export const MIN_CANVAS_WIDTH = 300;
export const MIN_CANVAS_HEIGHT = 200;

// Resize threshold - minimum pixel difference to trigger a resize
export const RESIZE_THRESHOLD = 5;

// Text styling constants
export const DEFAULT_TEXT_SIZE = 16;
export const DEFAULT_TEXT_SCALE = 1;

// Object ID generation constants
export const OBJECT_ID_PREFIX = "obj_";
export const OBJECT_ID_RANDOM_LENGTH = 11;

// Background image name identifier
export const BACKGROUND_IMAGE_NAME = "background-image";

// Crop rectangle name identifier
export const CROP_RECT_NAME = "crop-rect";

// Measurement object names
export const MEASUREMENT_LINE_NAME = "measurement-line";
export const MEASUREMENT_TEXT_NAME = "measurement-text";

// Object names that should be excluded from certain operations
export const EXCLUDED_OBJECT_NAMES = ["background-image", "crop-rect"];

// Default viewport transform (identity matrix)
export const IDENTITY_VIEWPORT_TRANSFORM: [number, number, number, number, number, number] = [
  1, 0, 0, 1, 0, 0
];

// Canvas configuration defaults
export const DEFAULT_CANVAS_SELECTION = true;
export const DEFAULT_CANVAS_BACKGROUND = "transparent";

// Object property defaults
export const DEFAULT_OBJECT_SELECTABLE = false;
export const DEFAULT_OBJECT_EVENTED = false;
export const DEFAULT_STROKE_UNIFORM = true;

// Filter default values
export const DEFAULT_BRIGHTNESS = 0;
export const DEFAULT_CONTRAST = 0;
export const DEFAULT_GRAYSCALE = false;
export const DEFAULT_INVERT = false;
export const DEFAULT_SHARPNESS = 0;
export const DEFAULT_GAMMA_R = 1;
export const DEFAULT_GAMMA_G = 1;
export const DEFAULT_GAMMA_B = 1;

// Transform state defaults
export const DEFAULT_ROTATIONS = 0;
export const DEFAULT_FLIP_HORIZONTAL = false;
export const DEFAULT_FLIP_VERTICAL = false;

// Rotation angles
export const ROTATION_ANGLES = {
  NONE: 0,
  QUARTER: 90,
  HALF: 180,
  THREE_QUARTER: 270,
} as const;

// Common rotation angles for positioning calculations
export const VERTICAL_ROTATION_ANGLES = [0, 180];
export const HORIZONTAL_ROTATION_ANGLES = [90, 270];
