/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON>vas, FabricText } from "fabric";
import { FabricMeasurementLine } from "@/shared/types";

export function formatDistance(distance: number): string {
  if (typeof distance !== "number" || isNaN(distance) || !isFinite(distance)) {
    return "0.00";
  }
  return `${(distance * 0.2645833333).toFixed(2)} mm`;
}

export function getImageScaleFactor(canvas: Canvas) {
  const bg = canvas.backgroundImage;
  if (!bg) return { x: 1, y: 1 };

  // These are the "original" image dimensions
  const intrinsicWidth = bg.width || 1;
  const intrinsicHeight = bg.height || 1;

  // These are the final on-canvas dimensions
  const renderedWidth = intrinsicWidth * (bg.scaleX || 1);
  const renderedHeight = intrinsicHeight * (bg.scaleY || 1);

  return {
    x: renderedWidth / intrinsicWidth, // Usually this will just be bg.scaleX
    y: renderedHeight / intrinsicHeight,
  };
}

export function createMeasurementText(line: FabricMeasurementLine, canvas: Canvas): FabricText {
  const lineCenter = line.getCenterPoint();
  // Absolute endpoints on canvas
  const x1 = line.left! + line.x1! * line.scaleX!;
  const y1 = line.top! + line.y1! * line.scaleY!;
  const x2 = line.left! + line.x2! * line.scaleX!;
  const y2 = line.top! + line.y2! * line.scaleY!;

  const dx = x2 - x1;
  const dy = y2 - y1;
  const scale = getImageScaleFactor(canvas);
  const realDx = dx / scale.x;
  const realDy = dy / scale.y;
  const realDistance = Math.sqrt(realDx * realDx + realDy * realDy);

  // Convert canvas px to mm (assuming 96 DPI)
  const pxToMM = 0.2645833333;
  const distanceMM = realDistance * pxToMM;

  const text = new FabricText(`${distanceMM.toFixed(2)} mm`, {
    fontSize: 16,
    left: lineCenter.x,
    top: lineCenter.y - 20,
    fill: "#ff0000",
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    selectable: false,
    evented: false,
    name: "measurement-text",
    originX: "center",
    originY: "center",
  });

  return text;
}

export function updateMeasurementText(canvas: Canvas, line: FabricMeasurementLine): void {
  if (!line.measurementText) return;

  const lineCenter = line.getCenterPoint();
  // Absolute endpoints on canvas
  const x1 = line.left! + line.x1! * line.scaleX!;
  const y1 = line.top! + line.y1! * line.scaleY!;
  const x2 = line.left! + line.x2! * line.scaleX!;
  const y2 = line.top! + line.y2! * line.scaleY!;

  const dx = x2 - x1;
  const dy = y2 - y1;
  const scale = getImageScaleFactor(canvas);
  const realDx = dx / scale.x;
  const realDy = dy / scale.y;
  const realDistance = Math.sqrt(realDx * realDx + realDy * realDy);

  // Convert canvas px to mm (assuming 96 DPI)
  const pxToMM = 0.2645833333;
  const distanceMM = realDistance * pxToMM;

  line.measurementText.set({
    text: `${distanceMM.toFixed(2)} mm`,
    left: lineCenter.x,
    top: lineCenter.y - 20,
  });

  canvas.renderAll();
}

export function updateMeasurementOnModify(canvas: Canvas, line: FabricMeasurementLine): void {
  if (!line.measurementText) return;

  const lineCenter = line.getCenterPoint();
  line.measurementText.set({
    left: lineCenter.x,
    top: lineCenter.y - 20,
  });

  canvas.renderAll();
}

export function cleanupOrphanedMeasurementTexts(canvas: Canvas): void {
  const allObjects = canvas.getObjects();
  const measurementLines = allObjects.filter((obj: any) => obj.name === "measurement-line");
  const measurementTexts = allObjects.filter((obj: any) => obj.name === "measurement-text");

  const linkedTexts = new Set();
  measurementLines.forEach((line: any) => {
    if (line.measurementText) {
      linkedTexts.add(line.measurementText);
    }
  });

  const orphanedTexts = measurementTexts.filter((text) => !linkedTexts.has(text));
  orphanedTexts.forEach((text) => {
    canvas.remove(text);
  });

  canvas.renderAll();
}

export function isMeasurementLine(obj: unknown): obj is FabricMeasurementLine {
  return (obj as any)?.name === "measurement-line";
}
