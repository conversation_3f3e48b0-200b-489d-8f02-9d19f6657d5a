import { Canvas, Rect } from "fabric";
import { CanvasConfig, FilterParams, SetupCanvasParams } from "@/shared/types";
import { loadAnnotations } from "../utils/annotations";
import { updateCanvasFilters } from "../utils/filters";
import { loadImage } from "../utils/loadImage";

const DEFAULT_CANVAS_CONFIG: CanvasConfig = {
  selection: true,
  backgroundColor: "transparent",
} as const;

const canvasFilterStates = new Map<Canvas, FilterParams>();

// Creates and configures an image canvas with image, filters, and annotations
export const createImageCanvas = async ({
  canvasElement,
  imageUrl,
  annotations,
  filters,
  cropData,
  existingCanvas,
}: SetupCanvasParams): Promise<{
  canvas: Canvas;
}> => {
  // Always use the original image URL since we use viewport transforms for cropping
  const finalImageSource = imageUrl;

  // Clean up existing canvas if present
  if (existingCanvas) {
    existingCanvas.dispose();
  }

  // Create new Fabric.js canvas with default configuration
  const canvas = new Canvas(canvasElement, {
    ...DEFAULT_CANVAS_CONFIG,
  });

  // Load background image - let loadImage handle canvas dimensions
  const containerRect = canvasElement.parentElement?.getBoundingClientRect();

  if (cropData?.isCropped && cropData.normalizedCropRect) {
    let imageLoadContainer = containerRect;
    if (cropData.canvasDimensions) {
      imageLoadContainer = {
        width: cropData.canvasDimensions.width,
        height: cropData.canvasDimensions.height,
        x: 0,
        y: 0,
        top: 0,
        left: 0,
        bottom: cropData.canvasDimensions.height,
        right: cropData.canvasDimensions.width,
        toJSON: () => ({}),
      } as DOMRect;
    }

    await loadImage(canvas, finalImageSource, {
      containerRect: imageLoadContainer || undefined,
    });

    const bgImage = canvas.backgroundImage!;
    const originalImageWidth = bgImage.width || 512;
    const originalImageHeight = bgImage.height || 512;
    const currentImageScale = bgImage.scaleX || 1;
    const scaledImageWidth = originalImageWidth * currentImageScale;
    const scaledImageHeight = originalImageHeight * currentImageScale;

    const left = cropData.normalizedCropRect.left * scaledImageWidth;
    const top = cropData.normalizedCropRect.top * scaledImageHeight;
    const width = cropData.normalizedCropRect.width * scaledImageWidth;
    const height = cropData.normalizedCropRect.height * scaledImageHeight;

    const clipRect = new Rect({
      left,
      top,
      width,
      height,
      absolutePositioned: true,
      selectable: false,
      evented: false,
    });
    canvas.clipPath = clipRect;

    const viewportContainer = cropData.canvasDimensions || containerRect;
    if (viewportContainer) {
      const containerBounds = viewportContainer;
      const aspectRatio = width / height;
      let newWidth = containerBounds.width;
      let newHeight = newWidth / aspectRatio;

      if (newHeight > containerBounds.height) {
        newHeight = containerBounds.height;
        newWidth = newHeight * aspectRatio;
      }

      canvas.setDimensions({ width: newWidth, height: newHeight });

      const scale = Math.max(newWidth / width, newHeight / height);
      const vpt: [number, number, number, number, number, number] = [
        scale,
        0,
        0,
        scale,
        -left * scale,
        -top * scale,
      ];
      canvas.setViewportTransform(vpt);
    }

    canvas.renderAll();
  } else {
    await loadImage(canvas, finalImageSource, {
      containerRect: containerRect || undefined,
    });
  }

  // Old crop logic completely removed - now handled above in the image loading section

  // Apply filters if provided
  if (filters) {
    canvasFilterStates.set(canvas, { ...filters });
    canvas.renderAll();
    updateCanvasFilters(canvas, filters);
  }

  // Load annotations if provided
  if (annotations) {
    await loadAnnotations(canvas, annotations);
  }

  // Configure canvas for viewer mode (non-interactive annotations)
  canvas.selection = false;
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "background-image") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  return {
    canvas,
  };
};
