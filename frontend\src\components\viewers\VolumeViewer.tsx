import { useRef, useEffect, useState } from "react";
import { Types, cache, RenderingEngine } from "@cornerstonejs/core";
import { ToolGroupManager } from "@cornerstonejs/tools";
import {
  initializeCornerstone,
  setup3dViewport,
  setup2dViewport,
  loadDicomVolume,
  loadDicomStack,
} from "@/lib/dicom/core";
import { saveVolumeConfig } from "@/shared/api";
import { VolumeViewerProps } from "@/shared/types";
import { useVolumeResize } from "@/hooks";
import { handleVolumeShiftChange } from "@/lib/dicom/handlers";
import { adjustVolumeShift } from "@/lib/dicom/utils";
import { setupViewer, volume2dModeConfig, volumeViewerConfig } from "@/lib/dicom/config";

const VolumeViewer: React.FC<VolumeViewerProps> = ({ data }) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const viewportRef = useRef<Types.IStackViewport | Types.IVolumeViewport | null>(null);
  const dicomFilesRef = useRef<string[]>([]);

  const renderingEngineId = `CSOIRenderingEngineVolume`;
  const viewportId = `CSOIViewportVolume`;
  const toolGroupId = `CSOIToolGroupVolume`;
  const dicomVolumeId = `CSOIDicomVolume`;
  const [isInitialized, setIsInitialized] = useState(false);
  const [is3D, setIs3D] = useState(true);
  const [shift, setShift] = useState(data.viewer.configs.shift);
  useVolumeResize(renderingEngineRef, viewportId, isInitialized);

  const handleSave = async () => {
    await saveVolumeConfig(data.id, { shift });
  };

  const switchTo3D = async () => {
    if (!elementRef.current || !renderingEngineRef.current) return;
    ToolGroupManager.destroyToolGroup(toolGroupId);
    const viewport = setup3dViewport(renderingEngineRef.current, elementRef.current, viewportId);
    viewportRef.current = viewport;
    setupViewer(toolGroupId, viewportId, renderingEngineId, volumeViewerConfig);

    await loadDicomVolume(viewport, dicomFilesRef.current, dicomVolumeId);
    adjustVolumeShift(viewport, shift);
    setIs3D(true);
  };
  const switchTo2D = async () => {
    if (!elementRef.current || !renderingEngineRef.current) return;
    ToolGroupManager.destroyToolGroup(toolGroupId);
    const viewport = setup2dViewport(renderingEngineRef.current, elementRef.current, viewportId);
    viewportRef.current = viewport;
    setupViewer(toolGroupId, viewportId, renderingEngineId, volume2dModeConfig);
    await loadDicomStack(viewport, dicomFilesRef.current);
    setIs3D(false);
  };

  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);

    return () => {
      ToolGroupManager.destroyToolGroup(toolGroupId);
      renderingEngineRef.current?.destroy();
      cache.purgeCache();
    };
  }, [toolGroupId]);

  useEffect(() => {
    if (!isInitialized || !elementRef.current) return;
    const initializeViewer = async () => {
      cache.purgeCache();
      const renderingEngine = new RenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;
      const element = elementRef.current;
      if (!element) return;
      const viewport = setup3dViewport(renderingEngine, element, viewportId);
      viewportRef.current = viewport;
      setupViewer(toolGroupId, viewportId, renderingEngineId, volumeViewerConfig);
      dicomFilesRef.current = data.viewer.imageUrl;
      await loadDicomVolume(viewport, data.viewer.imageUrl, dicomVolumeId);
      setIs3D(true);
      adjustVolumeShift(viewport, shift);
    };
    initializeViewer();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized, data.viewer.imageUrl]);

  return (
    <div className="volume-viewer">
      <div ref={elementRef} style={{ width: "100%", height: "100%" }} />

      {is3D && (
        <div className="controls">
          <div className="control-group">
            <label>Shift:</label>
            <input
              type="range"
              min="0"
              max="3000"
              step="100"
              value={shift}
              onChange={(e) =>
                handleVolumeShiftChange(
                  viewportRef.current as Types.IVolumeViewport,
                  parseInt(e.target.value),
                  setShift,
                  is3D
                )
              }
            />
            <span>{shift}</span>
          </div>

          <button className="save-button" onClick={handleSave}>
            Save Settings
          </button>
        </div>
      )}

      <div className="mode-switcher">
        <span className={`mode-switcher-item ${is3D ? "active" : "inactive"}`} onClick={switchTo3D}>
          3D
        </span>
        <span className="mode-switcher-separator">|</span>
        <span
          className={`mode-switcher-item ${!is3D ? "active" : "inactive"}`}
          onClick={switchTo2D}
        >
          2D
        </span>
      </div>
    </div>
  );
};

export default VolumeViewer;
