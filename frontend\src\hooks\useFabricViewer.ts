/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useRef, useState } from "react";
import { Canvas } from "fabric";
import { createHandleSave, createHandleShowOriginal } from "@/lib/fabric/handlers";
import { setupCanvasEventListeners } from "@/lib/fabric/utils/eventHandlers";
import {
  CropData,
  ImageViewerProps,
  FabricObjectState,
  TransformState,
  FilterParams,
} from "@/shared/types";
import { createImageCanvas } from "@/lib/fabric/core";
import { useUndoTracking } from "./useUndoTracking";
import { useFilterManagement } from "./useFilterManagement";
import { useImageTransforms } from "./useImageTransforms";
import { useCropManagement } from "./useCropManagement";

export const useFabricViewer = ({
  data,
  containerRef,
}: ImageViewerProps & {
  containerRef?: React.RefObject<HTMLElement | null>;
}) => {
  // Core canvas and tracking references
  const fabricCanvas = useRef<Canvas | null>(null);
  const eventDisposers = useRef<(() => void)[]>([]);
  const initialObjectCount = useRef(0);
  const originalImageUrl = useRef("");
  const objectStates = useRef(new Map<string, FabricObjectState>());
  const [isShowingOriginal, setIsShowingOriginal] = useState(false);

  // Initialize hooks
  const undoTracking = useUndoTracking(fabricCanvas, initialObjectCount);

  const filterManagement = useFilterManagement(
    {
      brightness: data.viewer.fabricConfigs.brightness,
      contrast: data.viewer.fabricConfigs.contrast,
      grayscale: data.viewer.fabricConfigs.grayscale,
      invert: data.viewer.fabricConfigs.invert,
      sharpness: data.viewer.fabricConfigs.sharpness,
      gammaR: data.viewer.fabricConfigs.gammaR,
      gammaG: data.viewer.fabricConfigs.gammaG,
      gammaB: data.viewer.fabricConfigs.gammaB,
    },
    fabricCanvas
  );

  const imageTransforms = useImageTransforms(
    fabricCanvas,
    data.viewer.fabricConfigs.transformState || {
      rotations: 0,
      flipHorizontal: false,
      flipVertical: false,
    }
  );

  const cropManagement = useCropManagement(
    fabricCanvas,
    data.viewer.fabricConfigs.cropData || {
      isCropped: false,
      normalizedCropRect: undefined,
      canvasDimensions: undefined,
    },
    undoTracking,
    containerRef,
    originalImageUrl
  );
  const isInitializingRef = useRef(false);
  const cachedState = useRef<{
    annotations: any;
    filters: any;
    cropData: CropData;
    transformState: TransformState;
  } | null>(null);

  const setupCanvas = useCallback(
    async (canvasElement: HTMLCanvasElement, imageSource: string) => {
      if (isInitializingRef.current) return;
      isInitializingRef.current = true;

      const result = await createImageCanvas({
        canvasElement,
        imageUrl: imageSource,
        annotations: data.viewer.fabricConfigs.annotations,
        filters: filterManagement.filters as FilterParams,
        cropData: data.viewer.fabricConfigs.cropData as CropData,
        existingCanvas: fabricCanvas.current,
      });

      fabricCanvas.current = result.canvas;
      originalImageUrl.current = imageSource;

      // Crop data is now properly initialized in useCropManagement
      // Only update if there's a significant change
      if (data.viewer.fabricConfigs.cropData) {
        const loadedCropData = data.viewer.fabricConfigs.cropData as CropData;
        if (loadedCropData.isCropped !== cropManagement.cropData.isCropped) {
          cropManagement.setCropData(loadedCropData);
          cropManagement.setHasPerformedCrop(loadedCropData.isCropped);
        }
      }
      const canvas = fabricCanvas.current;
      initialObjectCount.current = canvas.getObjects().length;

      // Clean up previous event listeners and setup new ones
      eventDisposers.current.forEach((dispose) => dispose());
      eventDisposers.current = setupCanvasEventListeners(canvas, undoTracking, objectStates);

      isInitializingRef.current = false;
    },
    [
      data.viewer.fabricConfigs.annotations,
      data.viewer.fabricConfigs.cropData,
      filterManagement.filters,
      cropManagement,
      undoTracking,
    ]
  );

  const handleSave = createHandleSave(
    fabricCanvas,
    data.id,
    filterManagement.filters,
    cropManagement.cropData,
    imageTransforms.transformState
  );

  const handleShowOriginal = createHandleShowOriginal(
    fabricCanvas,
    isShowingOriginal,
    setIsShowingOriginal,
    undoTracking.isUndoingRef,
    cachedState,
    filterManagement.filters,
    cropManagement.cropData,
    imageTransforms.transformState,
    originalImageUrl,
    cropManagement.setCropData,
    imageTransforms.setTransformState,
    cropManagement.setHasPerformedCrop,
    containerRef
  );

  // Destructure filter handlers for backward compatibility naming
  const {
    setBrightness: handleBrightnessChange,
    setContrast: handleContrastChange,
    setGrayscale: handleGrayscaleChange,
    setInvert: handleInvertChange,
    setSharpness: handleSharpnessChange,
    setGammaR: handleGammaRChange,
    setGammaG: handleGammaGChange,
    setGammaB: handleGammaBChange,
  } = filterManagement.filterHandlers;

  return {
    // Core canvas
    canvas: fabricCanvas,
    setupCanvas,

    // Filter management
    ...filterManagement.filters,
    ...filterManagement.filterHandlers,
    handleBrightnessChange,
    handleContrastChange,
    handleGrayscaleChange,
    handleInvertChange,
    handleSharpnessChange,
    handleGammaRChange,
    handleGammaGChange,
    handleGammaBChange,

    // Image transforms
    ...imageTransforms,

    // Undo functionality
    ...undoTracking,

    // Crop management
    ...cropManagement,

    // Additional handlers
    handleSave,
    handleShowOriginal,
    isShowingOriginal,
  };
};
