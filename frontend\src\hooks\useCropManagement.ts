import { useState } from "react";
import { Canvas } from "fabric";
import { CropData } from "@/shared/types";
import { handleCropOperation } from "@/lib/fabric/operations/crop";
import { UndoTrackingState } from "./useUndoTracking";

export interface CropManagementState {
  cropData: CropData;
  setCropData: React.Dispatch<React.SetStateAction<CropData>>;
  hasPerformedCrop: boolean;
  setHasPerformedCrop: React.Dispatch<React.SetStateAction<boolean>>;
  handleCrop: () => Promise<void>;
}

export const useCropManagement = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialCropData: CropData,
  undoTracking: UndoTrackingState,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
): CropManagementState => {
  const [cropData, setCropData] = useState<CropData>(initialCropData);
  const [hasPerformedCrop, setHasPerformedCrop] = useState(initialCropData.isCropped || false);

  const handleCrop = handleCropOperation(
    fabricCanvas,
    hasPerformedCrop,
    setCropData,
    undoTracking.isUndoingRef,
    setHasPerformedCrop,
    containerRef,
    originalImageUrl
  );

  return {
    cropData,
    setCropData,
    hasPerformedCrop,
    setHasPerformedCrop,
    handleCrop,
  };
};
